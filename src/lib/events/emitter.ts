import { EventData, EventType } from '@/lib/events/event-logger'
import { NotificationStorage } from '@/lib/notifications/notification-storage'

/**
 * Emits a generic event by appending it to NotificationStorage.
 * Automatically generates an id and timestamp.
 * @param event Partial event data excluding id and timestamp
 */
export function emitEvent(event: Omit<EventData, 'id' | 'timestamp'>) {
  const newEvent: EventData = {
    ...event,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
  }
  NotificationStorage.append(newEvent)
}

/**
 * Emits an 'invoice_paid' event.
 * @param actorId ID of the actor who triggered the event
 * @param targetId ID of the target user
 * @param projectId ID of the project
 * @param invoiceNumber Invoice number string
 * @param amount Paid amount number
 * @param projectTitle Optional project title
 */
export function emitInvoicePaid(
  actorId: string | number,
  targetId: string | number,
  projectId: string | number,
  invoiceNumber: string,
  amount: number,
  projectTitle?: string
) {
  emitEvent({
    type: 'invoice_paid' as EventType,
    actorId: String(actorId),
    targetId: String(targetId),
    context: {
      projectId: String(projectId),
      invoiceNumber,
    },
    metadata: {
      amount,
      projectTitle,
    },
    notificationType: 'success',
  })
}
